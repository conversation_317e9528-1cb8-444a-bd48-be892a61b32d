/* 组件样式文件 */

/* 消息气泡 */
.message-bubble {
    max-width: 70%;
    margin-bottom: 16px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble.own {
    margin-left: auto;
    margin-right: 0;
}

.message-bubble.other {
    margin-left: 0;
    margin-right: auto;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.message-sender {
    font-weight: 600;
    font-size: 0.85rem;
    color: var(--text-primary);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-left: auto;
}

.message-content {
    background: var(--surface-color);
    padding: 12px 16px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    position: relative;
}

.message-bubble.own .message-content {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.message-text {
    line-height: 1.4;
    word-wrap: break-word;
}

.message-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-bubble:hover .message-actions {
    opacity: 1;
}

.message-action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: var(--radius-sm);
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-action-btn:hover {
    background: rgba(0, 0, 0, 0.2);
}

.message-bubble.own .message-action-btn {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.message-bubble.own .message-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* 文件消息 */
.message-file {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--background-color);
    border-radius: var(--radius-md);
    margin-top: 8px;
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.file-download {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-sm);
    background: var(--primary-color);
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.file-download:hover {
    background: var(--primary-hover);
}

/* 图片消息 */
.message-image {
    margin-top: 8px;
    border-radius: var(--radius-md);
    overflow: hidden;
    max-width: 300px;
    cursor: pointer;
}

.message-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.2s ease;
}

.message-image:hover img {
    transform: scale(1.02);
}

/* 语音消息 */
.message-audio {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--background-color);
    border-radius: var(--radius-md);
    margin-top: 8px;
    min-width: 200px;
}

.audio-play-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.audio-play-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.audio-waveform {
    flex: 1;
    height: 30px;
    background: var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    overflow: hidden;
}

.audio-progress {
    height: 100%;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    width: 0%;
    transition: width 0.1s ease;
}

.audio-duration {
    font-size: 0.8rem;
    color: var(--text-secondary);
    flex-shrink: 0;
}

/* 系统消息 */
.system-message {
    text-align: center;
    margin: 20px 0;
    padding: 8px 16px;
    background: var(--background-color);
    border-radius: var(--radius-lg);
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* 正在输入指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    max-width: 200px;
}

.typing-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--secondary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    flex-shrink: 0;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--text-muted);
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 通知 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--surface-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 16px 20px;
    max-width: 400px;
    z-index: 1100;
    animation: notificationSlideIn 0.3s ease-out;
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.notification-close {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
}

/* 表单组件 */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--surface-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.form-checkbox label {
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.empty-state-description {
    font-size: 0.9rem;
    line-height: 1.5;
}

/* 文件上传 */
.file-upload-dialog {
    width: 100%;
    max-width: 500px;
}

.upload-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.upload-tabs .tab-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.upload-tabs .tab-btn:hover {
    color: var(--text-primary);
    background: var(--hover-bg);
}

.upload-tabs .tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.upload-tab {
    display: none;
}

.upload-tab.active {
    display: block;
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--hover-bg);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--primary-bg);
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
}

.upload-text {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.upload-hint {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.file-preview {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
}

.file-preview:empty {
    display: none;
}

.file-preview-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    background: var(--card-bg);
    position: relative;
}

.file-preview-item:last-child {
    margin-bottom: 0;
}

.preview-image img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 0.75rem;
}

.preview-audio {
    margin-right: 0.75rem;
}

.preview-audio audio {
    width: 200px;
    height: 30px;
}

.preview-file {
    margin-right: 0.75rem;
}

.file-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-bg);
    border-radius: 4px;
}

.preview-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.file-size {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.preview-remove {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 20px;
    height: 20px;
    border: none;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-remove:hover {
    background: var(--danger-hover);
}

/* 录音功能 */
.audio-recorder {
    margin: 1rem 0;
    text-align: center;
}

.recording-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.recording-dot {
    width: 8px;
    height: 8px;
    background: var(--danger-color);
    border-radius: 50%;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* 拖拽区域 */
.drop-zone {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.drop-zone-content {
    background: var(--card-bg);
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    border: 3px dashed var(--primary-color);
}

.drop-zone-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.drop-zone-text {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* 图片预览模态框 */
.image-preview-modal {
    text-align: center;
    max-width: 90vw;
    max-height: 90vh;
}

.image-preview-modal img {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 消息中的文件显示 */
.message-file {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--card-bg);
    border-radius: 8px;
    margin: 0.5rem 0;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-file:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

.message-file-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    opacity: 0.8;
}

.message-file-info {
    flex: 1;
}

.message-file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.message-file-size {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.message-image {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.message-image:hover {
    transform: scale(1.02);
}

.message-audio {
    width: 100%;
    max-width: 300px;
    margin: 0.5rem 0;
}

.audio-play-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 0.5rem;
    transition: background 0.2s ease;
}

.audio-play-btn:hover {
    background: var(--primary-hover);
}
